"use client";

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import GameConsole from './GameConsole';
import GameScreen from './GameScreen';

const RetroGamesSection = () => {
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game',
      color: '#4ade80', // Green
      icon: '🐍'
    },
    // Future games will be added here
    // {
    //   id: 'tetris',
    //   title: 'Tetris',
    //   description: 'Block puzzle game',
    //   color: '#3b82f6', // Blue
    //   icon: '🧩'
    // },
    // {
    //   id: 'duckhunt',
    //   title: 'Duck Hunt',
    //   description: 'Shoot the ducks',
    //   color: '#f59e0b', // Orange
    //   icon: '🦆'
    // }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);

      // Smooth scroll to center the game area
      setTimeout(() => {
        const gameArea = document.getElementById('game-area');
        if (gameArea) {
          gameArea.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100); // Small delay to ensure the game screen is rendered
    }, 500); // Transition duration
  };

  const handleBackToConsole = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  // Intersection Observer for scroll animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      {
        threshold: 0.3, // Trigger when 30% of section is visible
        rootMargin: '-100px 0px' // Start animation 100px before entering viewport
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section
      ref={sectionRef}
      id="retro-games-section"
      className="bg-background min-h-screen py-32 relative z-10"
    >
      {/* Subtle divider line */}
      <motion.div
        className="w-3/4 h-px bg-secondary/20 mx-auto mb-24"
        initial={{ opacity: 0, scaleX: 0 }}
        animate={isVisible ? { opacity: 1, scaleX: 1 } : { opacity: 0, scaleX: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      />

      {/* Main container - w-9/10 to match Projects section width */}
      <div className="w-9/10 mx-auto">
        <div className="min-h-screen flex">
          {/* Game Text - Left Side (1/3 of container) */}
          <motion.div
            className="w-1/3 flex items-center pl-8 lg:pl-12 bg-primary"
            initial={{ opacity: 0, y: 50 }}
            animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
          >
            <div className="w-full py-16">
              <motion.h2
                className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl mb-8"
                initial={{ opacity: 0, y: 30 }}
                animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
              >
                🎮 Retro Arcade
              </motion.h2>
              <motion.p
                className="text-secondary text-lg mb-8"
                initial={{ opacity: 0, y: 30 }}
                animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
              >
                Thanks for reaching the end! Here's a little treat - some classic games recreated with modern web tech.
              </motion.p>
              <motion.div
                className="text-secondary/60 text-sm"
                initial={{ opacity: 0, y: 30 }}
                animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.8, delay: 1.0, ease: "easeOut" }}
              >
                {!selectedGame ? (
                  <p>Click on a game card to start playing →</p>
                ) : (
                  <p>Enjoying the game? Use WASD or arrow keys to control!</p>
                )}
              </motion.div>
            </div>
          </motion.div>

          {/* Game Area - Right Side (2/3 of container) */}
          <motion.div
            className="w-2/3 flex items-center pr-8 lg:pr-12 relative"
            initial={{ opacity: 0, y: 50 }}
            animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
          >
            <div id="game-area" className="w-full bg-background py-16 flex items-center justify-center">
              {!selectedGame ? (
                <GameConsole
                  games={games}
                  onGameSelect={handleGameSelect}
                  isTransitioning={isTransitioning}
                />
              ) : (
                <GameScreen
                  game={selectedGame}
                  onBack={handleBackToConsole}
                  isTransitioning={isTransitioning}
                />
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default RetroGamesSection;
