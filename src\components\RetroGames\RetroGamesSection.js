"use client";

import { useState } from 'react';
import GameConsole from './GameConsole';
import GameScreen from './GameScreen';

const RetroGamesSection = () => {
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game',
      color: '#4ade80', // Green
      icon: '🐍'
    },
    // Future games will be added here
    // {
    //   id: 'tetris',
    //   title: 'Tetris',
    //   description: 'Block puzzle game',
    //   color: '#3b82f6', // Blue
    //   icon: '🧩'
    // },
    // {
    //   id: 'duckhunt',
    //   title: 'Duck Hunt',
    //   description: 'Shoot the ducks',
    //   color: '#f59e0b', // Orange
    //   icon: '🦆'
    // }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);

      // Smooth scroll to center the game area
      setTimeout(() => {
        const gameArea = document.getElementById('game-area');
        if (gameArea) {
          gameArea.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100); // Small delay to ensure the game screen is rendered
    }, 500); // Transition duration
  };

  const handleBackToConsole = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <section
      id="retro-games-section"
      className="bg-background min-h-screen py-20 relative z-10"
    >
      {/* Subtle divider line */}
      <div className="w-3/4 h-px bg-secondary/20 mx-auto mb-16"></div>

      {/* Container matching divider line width */}
      <div className="w-3/4 mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 min-h-[70vh]">
          {/* Game Text - Left Side (1 column) */}
          <div className="lg:col-span-1 flex items-center bg-primary rounded-2xl p-8">
            <div className="w-full">
              <h2 className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl mb-8">
                🎮 Retro Arcade
              </h2>
              <p className="text-secondary text-lg mb-8">
                Thanks for reaching the end! Here's a little treat - some classic games recreated with modern web tech.
              </p>
              <div className="text-secondary/60 text-sm">
                {!selectedGame ? (
                  <p>Click on a game card to start playing →</p>
                ) : (
                  <p>Enjoying the game? Use WASD or arrow keys to control!</p>
                )}
              </div>
            </div>
          </div>

          {/* Game Area - Right Side (2 columns) */}
          <div className="lg:col-span-2 flex items-center justify-center bg-background rounded-2xl p-8">
            {!selectedGame ? (
              <GameConsole
                games={games}
                onGameSelect={handleGameSelect}
                isTransitioning={isTransitioning}
              />
            ) : (
              <GameScreen
                game={selectedGame}
                onBack={handleBackToConsole}
                isTransitioning={isTransitioning}
              />
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default RetroGamesSection;
