"use client";

import { useState } from 'react';
import GameConsole from './GameConsole';
import GameScreen from './GameScreen';

const RetroGamesSection = () => {
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game',
      color: '#4ade80', // Green
      icon: '🐍'
    },
    // Future games will be added here
    // {
    //   id: 'tetris',
    //   title: 'Tetris',
    //   description: 'Block puzzle game',
    //   color: '#3b82f6', // Blue
    //   icon: '🧩'
    // },
    // {
    //   id: 'duckhunt',
    //   title: 'Duck Hunt',
    //   description: 'Shoot the ducks',
    //   color: '#f59e0b', // Orange
    //   icon: '🦆'
    // }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);

      // Smooth scroll to center the game area
      setTimeout(() => {
        const gameArea = document.getElementById('game-area');
        if (gameArea) {
          gameArea.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100); // Small delay to ensure the game screen is rendered
    }, 500); // Transition duration
  };

  const handleBackToConsole = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <>
      {/* Subtle divider line */}
      <div className="w-3/4 h-px bg-secondary/20 mx-auto mb-24 relative z-20"></div>

      {/* Combined Retro Games Container - Single fixed div for both text and games */}
      <div className="fixed inset-0 flex items-center justify-center z-10">
        {/* Main container - w-9/10 to match Projects section width */}
        <div className="w-9/10 h-screen flex">
          {/* Game Text - Left Side (1/3 of container) */}
          <div className="w-1/3 h-full flex items-center pl-8 lg:pl-12 bg-primary">
            <div className="w-full">
              <h2 className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl mb-8">
                🎮 Retro Arcade
              </h2>
              <p className="text-secondary text-lg mb-8">
                Thanks for reaching the end! Here's a little treat - some classic games recreated with modern web tech.
              </p>
              <div className="text-secondary/60 text-sm">
                {!selectedGame ? (
                  <p>Click on a game card to start playing →</p>
                ) : (
                  <p>Enjoying the game? Use WASD or arrow keys to control!</p>
                )}
              </div>
            </div>
          </div>

          {/* Game Area - Right Side (2/3 of container) */}
          <div className="w-2/3 h-full flex items-center pr-8 lg:pr-12 relative">
            <div id="game-area" className="w-full h-full bg-background flex items-center justify-center">
              {!selectedGame ? (
                <GameConsole
                  games={games}
                  onGameSelect={handleGameSelect}
                  isTransitioning={isTransitioning}
                />
              ) : (
                <GameScreen
                  game={selectedGame}
                  onBack={handleBackToConsole}
                  isTransitioning={isTransitioning}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Retro Games Section - provides scroll space */}
      <section
        id="retro-games-section"
        className="bg-background min-h-screen py-32 relative z-10"
      >
        <div className="w-full mx-auto px-6">
          {/* Invisible spacer to provide scroll area */}
          <div className="h-screen"></div>
        </div>
      </section>
    </>
  );
};

export default RetroGamesSection;
