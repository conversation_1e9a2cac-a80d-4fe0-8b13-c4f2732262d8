"use client";

import { useState } from 'react';
import GameConsole from './GameConsole';
import GameScreen from './GameScreen';

const RetroGamesSection = () => {
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game',
      color: '#4ade80', // Green
      icon: '🐍'
    },
    // Future games will be added here
    // {
    //   id: 'tetris',
    //   title: 'Tetris',
    //   description: 'Block puzzle game',
    //   color: '#3b82f6', // Blue
    //   icon: '🧩'
    // },
    // {
    //   id: 'duckhunt',
    //   title: 'Duck Hunt',
    //   description: 'Shoot the ducks',
    //   color: '#f59e0b', // Orange
    //   icon: '🦆'
    // }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);

      // Smooth scroll to center the game area
      setTimeout(() => {
        const gameArea = document.getElementById('game-area');
        if (gameArea) {
          gameArea.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100); // Small delay to ensure the game screen is rendered
    }, 500); // Transition duration
  };

  const handleBackToConsole = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <section
      id="retro-games-section"
      className="bg-background min-h-screen py-32 relative z-10"
    >
      {/* Subtle divider line */}
      <div className="w-3/4 h-px bg-secondary/20 mx-auto mb-24"></div>

      {/* Main container - same dimensions as Projects section but as normal element */}
      <div className="flex items-center justify-center min-h-screen">
        {/* Container with same width as Projects (90% of screen) */}
        <div className="w-[90vw] h-screen flex">
          {/* Game Text - Left Side (1/3 of container) - same as Projects */}
          <div className="w-1/3 h-full flex items-center pl-8 lg:pl-12 bg-primary">
            <div className="w-full">
              <h2 className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl mb-8">
                🎮 Retro Arcade
              </h2>
              <p className="text-secondary text-lg mb-8">
                Thanks for reaching the end! Here's a little treat - some classic games recreated with modern web tech.
              </p>
              <div className="text-secondary/60 text-sm">
                {!selectedGame ? (
                  <p>Click on a game card to start playing →</p>
                ) : (
                  <p>Enjoying the game? Use WASD or arrow keys to control!</p>
                )}
              </div>
            </div>
          </div>

          {/* Game Area - Right Side (2/3 of container) - same as Projects */}
          <div className="w-2/3 h-full flex items-center pr-8 lg:pr-12 relative">
            <div id="game-area" className="w-full h-full bg-background flex items-center justify-center p-4">
              <div
                className="w-full max-w-full"
                style={{
                  // Constrain width to respect the 2/3 container boundaries
                  maxWidth: 'calc(100vw * 0.9 * 0.67 - 6rem)', // 90% screen * 2/3 - padding
                }}
              >
                {!selectedGame ? (
                  <GameConsole
                    games={games}
                    onGameSelect={handleGameSelect}
                    isTransitioning={isTransitioning}
                  />
                ) : (
                  <GameScreen
                    game={selectedGame}
                    onBack={handleBackToConsole}
                    isTransitioning={isTransitioning}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RetroGamesSection;
