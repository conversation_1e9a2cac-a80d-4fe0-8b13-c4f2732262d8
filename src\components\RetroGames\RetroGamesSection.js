"use client";

import { useState } from 'react';
import GameConsole from './GameConsole';
import GameScreen from './GameScreen';

const RetroGamesSection = () => {
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game',
      color: '#4ade80', // Green
      icon: '🐍'
    },
    // Future games will be added here
    // {
    //   id: 'tetris',
    //   title: 'Tetris',
    //   description: 'Block puzzle game',
    //   color: '#3b82f6', // Blue
    //   icon: '🧩'
    // },
    // {
    //   id: 'duckhunt',
    //   title: 'Duck Hunt',
    //   description: 'Shoot the ducks',
    //   color: '#f59e0b', // Orange
    //   icon: '🦆'
    // }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);

      // Smooth scroll to center the game area
      setTimeout(() => {
        const gameArea = document.getElementById('game-area');
        if (gameArea) {
          gameArea.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100); // Small delay to ensure the game screen is rendered
    }, 500); // Transition duration
  };

  const handleBackToConsole = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <section
      id="retro-games-section"
      className="bg-background min-h-screen py-20 relative z-10"
    >
      {/* Subtle divider line */}
      <div className="w-3/4 h-px bg-secondary/20 mx-auto mb-24"></div>

      {/* Main container - using standard max-width like other sections */}
      <div className="max-w-6xl mx-auto px-6">
        <div className="flex min-h-[70vh]">
          {/* Game Text - Left Side (1/3 of container) */}
          <div className="w-1/3 flex items-center bg-primary rounded-l-2xl px-8">
            <div className="w-full">
              <h2 className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl mb-8">
                🎮 Retro Arcade
              </h2>
              <p className="text-secondary text-lg mb-8">
                Thanks for reaching the end! Here's a little treat - some classic games recreated with modern web tech.
              </p>
              <div className="text-secondary/60 text-sm">
                {!selectedGame ? (
                  <p>Click on a game card to start playing →</p>
                ) : (
                  <p>Enjoying the game? Use WASD or arrow keys to control!</p>
                )}
              </div>
            </div>
          </div>

          {/* Game Area - Right Side (2/3 of container) */}
          <div className="w-2/3 flex items-center bg-background rounded-r-2xl px-8">
            <div id="game-area" className="w-full flex items-center justify-center py-16">
              {!selectedGame ? (
                <GameConsole
                  games={games}
                  onGameSelect={handleGameSelect}
                  isTransitioning={isTransitioning}
                />
              ) : (
                <GameScreen
                  game={selectedGame}
                  onBack={handleBackToConsole}
                  isTransitioning={isTransitioning}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RetroGamesSection;
