"use client";

import { motion } from 'framer-motion';

const GameConsole = ({ games, onGameSelect, isTransitioning }) => {
  return (
    <motion.div
      className="w-full max-w-3xl"
      initial={{ opacity: 1, scale: 1 }}
      animate={{
        opacity: isTransitioning ? 0 : 1,
        scale: isTransitioning ? 0.9 : 1
      }}
      transition={{ duration: 0.5, ease: "easeInOut" }}
    >
      {/* Game Cards Grid - Optimized for 2/3 layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {games.map((game) => (
            <motion.div
              key={game.id}
              className="bg-background rounded-2xl p-6 cursor-pointer group hover:bg-background/80 transition-all duration-300 shadow-lg hover:shadow-xl"
              whileHover={{ scale: 1.05, y: -5 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onGameSelect(game)}
            >
              {/* Game Content */}
              <div className="text-center">
                <div
                  className="w-16 h-16 mx-auto rounded-2xl flex items-center justify-center text-3xl mb-3 group-hover:scale-110 transition-transform duration-300"
                  style={{ backgroundColor: game.color + '20' }}
                >
                  {game.icon}
                </div>
                <h4 className="font-heading font-bold text-secondary text-lg mb-2">
                  {game.title}
                </h4>
                <p className="text-secondary/70 text-sm">
                  {game.description}
                </p>
              </div>
            </motion.div>
          ))}

          {/* Coming Soon Cards */}
          {[...Array(2)].map((_, index) => (
            <motion.div
              key={`coming-soon-${index}`}
              className="bg-background/30 rounded-2xl p-6 opacity-40"
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto rounded-2xl bg-secondary/10 flex items-center justify-center text-3xl mb-3">
                  🎮
                </div>
                <h4 className="font-heading font-bold text-secondary/50 text-lg mb-2">
                  Coming Soon
                </h4>
                <p className="text-secondary/30 text-sm">
                  More games on the way
                </p>
              </div>
            </motion.div>
          ))}
        </div>
    </motion.div>
  );
};

export default GameConsole;
