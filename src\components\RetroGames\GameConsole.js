"use client";

import { motion } from 'framer-motion';

const GameConsole = ({ games, onGameSelect, isTransitioning }) => {
  return (
    <motion.div
      className="max-w-4xl w-full"
      initial={{ opacity: 1, scale: 1 }}
      animate={{
        opacity: isTransitioning ? 0 : 1,
        scale: isTransitioning ? 0.9 : 1
      }}
      transition={{ duration: 0.5, ease: "easeInOut" }}
    >
      {/* Game Cards Grid - Floating cards */}
      <div className="grid md:grid-cols-3 gap-8">
          {games.map((game) => (
            <motion.div
              key={game.id}
              className="bg-background rounded-2xl p-8 cursor-pointer group hover:bg-background/80 transition-all duration-300 shadow-lg hover:shadow-xl"
              whileHover={{ scale: 1.05, y: -5 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onGameSelect(game)}
            >
              {/* Game Content */}
              <div className="text-center">
                <div
                  className="w-20 h-20 mx-auto rounded-2xl flex items-center justify-center text-4xl mb-4 group-hover:scale-110 transition-transform duration-300"
                  style={{ backgroundColor: game.color + '20' }}
                >
                  {game.icon}
                </div>
                <h4 className="font-heading font-bold text-secondary text-xl mb-2">
                  {game.title}
                </h4>
                <p className="text-secondary/70 text-sm">
                  {game.description}
                </p>
              </div>
            </motion.div>
          ))}

          {/* Coming Soon Cards */}
          {[...Array(2)].map((_, index) => (
            <motion.div
              key={`coming-soon-${index}`}
              className="bg-background/30 rounded-2xl p-8 opacity-40"
            >
              <div className="text-center">
                <div className="w-20 h-20 mx-auto rounded-2xl bg-secondary/10 flex items-center justify-center text-4xl mb-4">
                  🎮
                </div>
                <h4 className="font-heading font-bold text-secondary/50 text-xl mb-2">
                  Coming Soon
                </h4>
                <p className="text-secondary/30 text-sm">
                  More games on the way
                </p>
              </div>
            </motion.div>
          ))}
        </div>
    </motion.div>
  );
};

export default GameConsole;
