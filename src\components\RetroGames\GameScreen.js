"use client";

import { motion } from 'framer-motion';
import Button from '../Button';
import SnakeG<PERSON> from './games/SnakeGame';

const GameScreen = ({ game, onBack, isTransitioning }) => {
  const renderGame = () => {
    switch (game.id) {
      case 'snake':
        return <SnakeGame />;
      // Future games will be added here
      // case 'tetris':
      //   return <TetrisGame />;
      // case 'duckhunt':
      //   return <DuckHuntGame />;
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-secondary text-xl">Game not found</p>
          </div>
        );
    }
  };

  return (
    <motion.div
      className="w-full max-w-4xl"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{
        opacity: isTransitioning ? 0 : 1,
        scale: isTransitioning ? 0.8 : 1
      }}
      transition={{ duration: 0.5, ease: "easeInOut" }}
    >
      <div className="space-y-6">
        {/* Game Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <div
              className="w-8 h-8 rounded-xl flex items-center justify-center text-lg"
              style={{ backgroundColor: game.color + '20' }}
            >
              {game.icon}
            </div>
            <div>
              <h3 className="font-heading font-bold text-secondary text-xl">
                {game.title}
              </h3>
              <p className="text-secondary/70 text-xs">
                {game.description}
              </p>
            </div>
          </div>

          <Button
            variant="outline"
            onClick={onBack}
            className="hover:bg-accent hover:text-white hover:border-accent text-sm px-4 py-2"
          >
            ← Back
          </Button>
        </div>

        {/* CRT Game Screen */}
        <div className="crt-screen active bg-black rounded-3xl overflow-hidden shadow-2xl" style={{ aspectRatio: '4/3' }}>
          {/* Game Content */}
          <div className="w-full h-full relative crt-content">
            {renderGame()}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default GameScreen;
