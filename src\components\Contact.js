"use client";

import { useRef, useState, useEffect } from 'react';
import Button from './Button';
import { useModal } from '../contexts/ModalContext';

const Contact = () => {
  const sectionRef = useRef(null);
  const { openModal } = useModal();

  return (
    <section
      ref={sectionRef}
      data-section="contact"
      className="bg-background min-h-screen py-20 relative z-20"
    >
      <div className="max-w-6xl mx-auto px-6">
        {/* Contact Header */}
        <div className="text-center mb-16">
          <h2 className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl mb-6">
            Let's Connect
          </h2>
          <p className="text-secondary text-lg max-w-2xl mx-auto">
            Ready to bring your vision to life? Let's discuss your project and create something amazing together.
          </p>
        </div>

        {/* Contact Content */}
        <div className="grid md:grid-cols-2 gap-12 mb-20">
          {/* Contact Info */}
          <div className="space-y-8">
            <div>
              <h3 className="font-heading font-bold text-secondary text-2xl mb-4">
                Get In Touch
              </h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <svg className="w-5 h-5 text-secondary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-secondary hover:text-accent transition-colors duration-200"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-heading font-bold text-secondary text-2xl mb-4">
                Follow Me
              </h3>
              <div className="flex space-x-4">
                <a
                  href="http://linkedin.com/in/teodor-cretu"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-12 h-12 bg-primary border border-secondary/20 rounded-lg flex items-center justify-center hover:bg-secondary hover:text-primary transition-colors"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a
                  href="http://www.be.net/teodorcretu"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-12 h-12 bg-primary border border-secondary/20 rounded-lg flex items-center justify-center hover:bg-secondary hover:text-primary transition-colors"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M6.5 12C5.11 12 4 13.11 4 14.5S5.11 17 6.5 17 9 15.89 9 14.5 7.89 12 6.5 12M17.5 12C16.11 12 15 13.11 15 14.5S16.11 17 17.5 17 20 15.89 20 14.5 18.89 12 17.5 12M12 7C10.61 7 9.5 8.11 9.5 9.5S10.61 12 12 12 14.5 10.89 14.5 9.5 13.39 7 12 7M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2M12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20Z"/>
                  </svg>
                </a>
              </div>
            </div>

            {/* Action Buttons */}
            <div>
              <h3 className="font-heading font-bold text-secondary text-2xl mb-4">
                Actions
              </h3>
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => openModal('resume')}
                >
                  View Resume
                </Button>
                <Button
                  variant="filled"
                  onClick={() => openModal('project-wizard')}
                >
                  Request Project
                </Button>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div>
            <h3 className="font-heading font-bold text-secondary text-2xl mb-4">
              Or just say hi!
            </h3>
            <div className="bg-primary rounded-2xl border border-secondary/20 p-8">
              <form className="space-y-6">
              <div>
                <label className="block text-secondary text-sm font-medium mb-2">
                  Name
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-3 bg-background border border-secondary/20 rounded-lg text-secondary placeholder-secondary/50 focus:outline-none focus:border-secondary"
                  placeholder="Your name"
                />
              </div>
              <div>
                <label className="block text-secondary text-sm font-medium mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="w-full px-4 py-3 bg-background border border-secondary/20 rounded-lg text-secondary placeholder-secondary/50 focus:outline-none focus:border-secondary"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-secondary text-sm font-medium mb-2">
                  Message
                </label>
                <textarea
                  rows={4}
                  className="w-full px-4 py-3 bg-background border border-secondary/20 rounded-lg text-secondary placeholder-secondary/50 focus:outline-none focus:border-secondary resize-none"
                  placeholder="Tell me about your project..."
                ></textarea>
              </div>
              <Button
                type="submit"
                variant="filled"
                className="w-full"
              >
                Send Message
              </Button>
            </form>
            </div>
          </div>
        </div>

        {/* Bonus Discovery Arrow - Points to retro games section */}
        <div className="flex justify-center mt-16">
          <div className="text-center">
            <p className="text-secondary/60 text-sm mb-4 font-medium">
              Psst... there's a little bonus below 👇
            </p>
            <div className="scroll-down-arrow">
              <svg
                className="w-6 h-6 text-accent mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 14l-7 7m0 0l-7-7m7 7V3"
                />
              </svg>
            </div>
          </div>
        </div>

      </div>
    </section>
  );
};

export default Contact;
